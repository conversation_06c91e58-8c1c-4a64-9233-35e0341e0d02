/// -----
/// reset_password_screen.dart
///
/// 重置密码页面，提供重置密码的用户界面
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../bloc/reset_password/reset_password_bloc.dart';
import '../bloc/reset_password/reset_password_event.dart';
import '../bloc/reset_password/reset_password_state.dart';
import '../../../../core/utils/device_utils.dart';
import '../../../../core/utils/validators.dart';
import '../../../../core/widgets/app_snack_bar.dart';
import '../../../../core/widgets/immersive_app_bar.dart';
import '../../../../core/router/app_navigator.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// 重置密码页面
///
/// 提供重置密码的用户界面
class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneFocusNode = FocusNode();
  final _codeFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  // 焦点状态变量在CustomTextField内部已处理，这里不需要额外的状态变量

  // 错误信息
  String? _phoneError;
  String? _codeError;
  String? _passwordError;

  int _countdown = 0;
  bool _isLoading = false;
  Timer? _countdownTimer;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _phoneFocusNode.dispose();
    _codeFocusNode.dispose();
    _passwordFocusNode.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    // 取消可能存在的旧定时器
    _countdownTimer?.cancel();

    // 设置初始倒计时时间
    setState(() {
      _countdown = 60;
    });

    // 创建新的定时器，每秒触发一次
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown <= 1) {
        // 倒计时结束，取消定时器
        timer.cancel();
        _countdownTimer = null;
        setState(() {
          _countdown = 0;
        });
      } else {
        // 倒计时递减
        setState(() {
          _countdown--;
        });
      }
    });
  }

  Future<void> _sendVerificationCode() async {
    // 验证手机号
    final String? phoneError = Validators.validateMobile(_phoneController.text);
    setState(() {
      _phoneError = phoneError;
    });

    if (phoneError != null) {
      AppSnackBar.showError(context, phoneError);
      return;
    }

    final deviceId = await DeviceUtils.getDeviceId();
    if (mounted) {
      context.read<ResetPasswordBloc>().add(
            SendVerificationCodeEvent(
              phone: _phoneController.text,
              deviceId: deviceId,
            ),
          );
    }
  }

  Future<void> _resetPassword() async {
    // 验证手机号
    final String? phoneError = Validators.validateMobile(_phoneController.text);
    // 验证验证码
    final String? codeError = Validators.validateVerificationCode(_codeController.text);
    // 验证密码
    final String? passwordError = Validators.validatePassword(_passwordController.text);

    setState(() {
      _phoneError = phoneError;
      _codeError = codeError;
      _passwordError = passwordError;
    });

    // 如果有错误，不进行重置密码
    if (phoneError != null || codeError != null || passwordError != null) {
      return;
    }

    // 设置加载状态
    setState(() {
      _isLoading = true;
    });

    final deviceId = await DeviceUtils.getDeviceId();
    if (mounted) {
      context.read<ResetPasswordBloc>().add(
            ResetPasswordSubmittedEvent(
              phone: _phoneController.text,
              code: _codeController.text,
              newPassword: _passwordController.text,
              deviceId: deviceId,
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 状态栏背景透明
        statusBarIconBrightness: Brightness.dark, // 状态栏图标为深色
        statusBarBrightness: Brightness.light, // iOS状态栏亮度
      ),
      child: PopScope(
        canPop: false,
        onPopInvoked: (didPop) {
          if (!didPop) {
            AppNavigator.goToLogin(context);
          }
        },
        child: Scaffold(
          // 确保Scaffold不会为AppBar预留空间
          extendBodyBehindAppBar: true,
          // 使用沉浸式AppBar
          appBar: ImmersiveAppBar(
            onBackPressed: () => AppNavigator.goToLogin(context),
          ),
          // 使用Stack作为主体布局
          body: Stack(
            children: [
              // 背景图层 - 覆盖整个屏幕包括状态栏
              Positioned.fill(
                child: Image.asset(
                  'assets/images/register_top_bg.png',
                  fit: BoxFit.cover,
                  alignment: Alignment.topCenter,
                ),
              ),

              // 内容图层
              BlocListener<ResetPasswordBloc, ResetPasswordState>(
                listener: (context, state) {
                  if (state is ResetPasswordLoading) {
                    setState(() {
                      _isLoading = true;
                    });
                  } else {
                    setState(() {
                      _isLoading = false;
                    });
                  }

                  if (state is VerificationCodeSent) {
                    _startCountdown();
                    AppSnackBar.showSuccess(context, '验证码发送成功');
                  }

                  if (state is PasswordResetSuccess) {
                    AppSnackBar.showSuccess(context, '密码重置成功');

                    // 短暂延迟后返回登录页，让用户有时间看到成功提示
                    Future.delayed(const Duration(milliseconds: 1500), () {
                      if (context.mounted) {
                        AppNavigator.goToLogin(context);
                      }
                    });
                  }

                  if (state is ResetPasswordError) {
                    AppSnackBar.showError(context, state.message);
                  }
                },
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 75.w),
                    child: Form(
                      key: _formKey,
                      autovalidateMode: AutovalidateMode.disabled,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 顶部内边距，考虑AppBar高度
                          SizedBox(height: 180.h),

                          // 重置密码标题
                          Text(
                            '重置密码',
                            style: TextStyle(
                              fontSize: 48.sp,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF333333),
                            ),
                          ),

                          SizedBox(height: 60.h),

                          // 手机号输入框
                          CustomTextField(
                            label: '手机号码',
                            hintText: '请输入手机号码',
                            controller: _phoneController,
                            focusNode: _phoneFocusNode,
                            keyboardType: TextInputType.phone,
                            errorText: _phoneError,
                            onChanged: (value) {
                              setState(() {
                                _phoneError = Validators.validateMobile(value);
                              });
                            },
                          ),

                          SizedBox(height: 40.h),

                          // 验证码输入框
                          CustomTextField(
                            label: '短信验证码',
                            hintText: '请输入短信验证码',
                            controller: _codeController,
                            focusNode: _codeFocusNode,
                            keyboardType: TextInputType.number,
                            errorText: _codeError,
                            suffixIcon: TextButton(
                              onPressed: _countdown > 0 ? null : _sendVerificationCode,
                              style: TextButton.styleFrom(
                                minimumSize: Size.zero,
                                padding: EdgeInsets.symmetric(horizontal: 16.w),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: Text(
                                _countdown > 0 ? '${_countdown}s后重发' : '发送验证码',
                                style: TextStyle(
                                  color: _countdown > 0 ? Colors.grey : const Color(0xFF2165F6),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 28.sp,
                                ),
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                _codeError = Validators.validateVerificationCode(value);
                              });
                            },
                          ),

                          SizedBox(height: 40.h),

                          // 密码输入框
                          CustomTextField(
                            label: '设置登录密码',
                            hintText: '请设置登录密码',
                            controller: _passwordController,
                            focusNode: _passwordFocusNode,
                            obscureText: true,
                            errorText: _passwordError,
                            onChanged: (value) {
                              setState(() {
                                _passwordError = Validators.validatePassword(value);
                              });
                            },
                          ),

                          SizedBox(height: 80.h),

                          // 确认按钮
                          SizedBox(
                            width: double.infinity,
                            height: 88.h,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _resetPassword,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF2165F6),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10.r),
                                ),
                                elevation: 0,
                              ),
                              child: _isLoading
                                  ? SizedBox(
                                      width: 40.w,
                                      height: 40.h,
                                      child: const CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : Text(
                                      '确认',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 32.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                            ),
                          ),

                          SizedBox(height: 60.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
