import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/utils/validators.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// 登录表单
///
/// 显示用户名和密码输入框，以及登录、注册和忘记密码按钮
class LoginForm extends StatefulWidget {
  /// 登录回调
  final Function(String username, String password) onLogin;

  /// 注册回调
  final VoidCallback onRegister;

  /// 忘记密码回调
  final VoidCallback onForgotPassword;

  const LoginForm({
    Key? key,
    required this.onLogin,
    required this.onRegister,
    required this.onForgotPassword,
  }) : super(key: key);

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscureText = true;
  bool _isLoading = false;
  String? _phoneError;
  String? _passwordError;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _submitForm() {
    // 验证手机号
    final String? phoneError = Validators.validateMobile(_phoneController.text);
    // 验证密码
    final String? passwordError = Validators.validatePassword(_passwordController.text);

    setState(() {
      _phoneError = phoneError;
      _passwordError = passwordError;
    });

    // 如果有错误，不进行登录
    if (phoneError != null || passwordError != null) {
      return;
    }

    // 设置加载状态
    setState(() {
      _isLoading = true;
    });

    // 调用登录回调
    widget.onLogin(
      _phoneController.text,
      _passwordController.text,
    );

    // 登录后重置加载状态
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 顶部背景图
            Image.asset(
              'assets/images/login_ai_platform_icon.png',
              width: double.infinity,
              fit: BoxFit.cover,
            ),

            // 内容区域 - 使用带圆角的容器
            Transform.translate(
              offset: const Offset(0, -20),
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 30),

                        // 手机号输入框
                        CustomTextField(
                          label: '手机号码',
                          hintText: '请输入手机号',
                          controller: _phoneController,
                          keyboardType: TextInputType.phone,
                          errorText: _phoneError,
                          backgroundColor: const Color(0xFFF5F5F5),
                          borderRadius: 8,
                          onChanged: (value) {
                            setState(() {
                              _phoneError = Validators.validateMobile(value);
                            });
                          },
                        ),

                        const SizedBox(height: 20),

                        // 密码输入框
                        CustomTextField(
                          label: '密码',
                          hintText: '请输入密码',
                          controller: _passwordController,
                          obscureText: true,
                          errorText: _passwordError,
                          backgroundColor: const Color(0xFFF5F5F5),
                          borderRadius: 8,
                          onChanged: (value) {
                            setState(() {
                              _passwordError = Validators.validatePassword(value);
                            });
                          },
                        ),

                        // 忘记密码按钮
                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: TextButton(
                              onPressed: widget.onForgotPassword,
                              style: TextButton.styleFrom(
                                minimumSize: Size.zero,
                                padding: EdgeInsets.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: const Text(
                                '忘记密码?',
                                style: TextStyle(
                                  color: Color(0xFF2979FF),
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 40),

                        // 登录按钮
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _submitForm,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2979FF),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2.0,
                                    ),
                                  )
                                : const Text(
                                    '登录',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                          ),
                        ),

                        // 创建账号按钮
                        Align(
                          alignment: Alignment.center,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 20),
                            child: TextButton(
                              onPressed: widget.onRegister,
                              child: const Text(
                                '注册',
                                style: TextStyle(
                                  color: Color(0xFF2979FF),
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
