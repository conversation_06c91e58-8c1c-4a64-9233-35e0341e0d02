/// -----
/// student_internship_plan_list_screen.dart
/// 
/// 学生端实习计划页面，展示学生可参与的实习计划列表
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_application_screen.dart';
import 'package:flutter_demo/features/plan/presentation/screens/student_exemption_application_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/teacher_internship_plan_detail_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/state_mapper.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/page_state_widget.dart';
import 'package:flutter_demo/features/plan/data/models/internship_plan_info.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/plan_list_bloc.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/plan_list_event.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/plan_list_state.dart';
import 'package:flutter_demo/features/plan/presentation/widgets/internship_plan_card.dart';
import 'package:go_router/go_router.dart';

import 'student_exemption_application_screen.dart';

/// 学生端实习计划列表页面
///
/// 展示学生可参与的实习计划列表，支持查看详情、申请实习和申请免实习
class StudentInternshipPlanListScreen extends StatelessWidget {
  const StudentInternshipPlanListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PlanListBloc()..add(const LoadPlanListEvent()),
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: const CustomAppBar(
          title: '实习计划',
          showBackButton: true,
        ),
        body: BlocBuilder<PlanListBloc, PlanListState>(
          builder: (context, state) {
            return PageStateWidget(
              state: StateMapper.mapPlanListState(state),
              onRetry: () => context.read<PlanListBloc>().add(const LoadPlanListEvent()),
              child: state is PlanListLoaded ? _buildPlanList(context, state.plans) : null,
              loadingMessage: '加载实习计划中...',
              emptyMessage: '暂无实习计划',
              errorMessage: state is PlanListError ? state.message : '加载失败',
            );
          },
        ),
      ),
    );
  }

  /// 构建计划列表
  Widget _buildPlanList(BuildContext context, List<InternshipPlanInfo> plans) {
    if (plans.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment_outlined, size: 80.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            Text(
              '暂无实习计划',
              style: TextStyle(fontSize: 32.sp, color: Colors.grey[700]),
            ),
            SizedBox(height: 8.h),
            Text(
              '当前没有可参与的实习计划',
              style: TextStyle(fontSize: 28.sp, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<PlanListBloc>().add(const RefreshPlanListEvent());
      },
      child: ListView.builder(
        padding: EdgeInsets.only(bottom: 36.w),
        itemCount: plans.length,
        itemBuilder: (context, index) {
          return InternshipPlanCard(
            plan: plans[index],
            onTap: () {
              // 导航到实习计划详情页，传递isStudent=true参数
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => TeacherInternshipPlanDetailScreen(
                    planId: plans[index].id,
                    isStudent: true, // 学生端显示底部按钮
                    onExemptionPressed: (planId) {
                      // 点击免实习按钮
                      _navigateToExemptionApplication(context,plans[index]);
                    },
                    onApplyPressed: (planId) {
                      _navigateToInternshipApplication(context, planId);
                    },
                  ),
                ),
              );
            },
            onApply: (planId) {
              // 点击卡片上的“实习申请”按钮，跳转到实习申请页面
              _navigateToInternshipApplication(context, planId);
            },
            onExempt: (planId) {
              // 点击卡片上的“免实习”按钮
              // context.read<PlanListBloc>().add(ApplyExemptionEvent(planId: planId));
              _navigateToExemptionApplication(context,plans[index]);
            },
          );
        },
      ),
    );
  }

  // 导航到实习申请页面
  void _navigateToInternshipApplication(BuildContext context, String planId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InternshipApplicationScreen(
          planId: planId,
        ),
      ),
    );
  }

  // 导航到免实习申请页面
  void _navigateToExemptionApplication(BuildContext context,InternshipPlanInfo plan) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StudentExemptionApplicationScreen(),
      ),
    );
  }
}